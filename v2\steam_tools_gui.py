import customtkinter as ctk
from tkinter import messagebox, filedialog, simpledialog
import threading
import requests
import json
import os
import sys
import hashlib
import winreg
from pathlib import Path
import time
from datetime import datetime
import shutil
import traceback
import platform
import uuid
import inspect
import psutil
import ctypes

# Add parent directory to path to import keyauth
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from keyauth import api

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

def is_admin():
    """Check if the current process has administrator privileges"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """Restart the current script with administrator privileges"""
    if is_admin():
        return True
    else:
        try:
            # Re-run the current script with admin privileges
            ctypes.windll.shell32.ShellExecuteW(
                None,
                "runas",
                sys.executable,
                " ".join(sys.argv),
                None,
                1
            )
            return True
        except:
            return False

class AdminWarningDialog:
    """Custom administrator warning dialog using CustomTkinter"""

    def __init__(self, parent):
        self.parent = parent
        self.result = None

        # Create dialog window
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("Administrator Required")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)

        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.center_dialog()

        # Create content
        self.create_content()

        # Focus on dialog
        self.dialog.focus()

    def center_dialog(self):
        """Center the dialog on the parent window"""
        self.dialog.update_idletasks()

        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # Calculate center position
        dialog_width = 500
        dialog_height = 400
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def create_content(self):
        """Create the dialog content"""
        # Main frame
        main_frame = ctk.CTkFrame(self.dialog, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Warning icon and title
        title_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        title_frame.pack(fill="x", pady=(0, 20))

        # Warning icon (using emoji)
        icon_label = ctk.CTkLabel(title_frame, text="⚠️", font=ctk.CTkFont(size=48))
        icon_label.pack(side="left", padx=(0, 15))

        # Title
        title_label = ctk.CTkLabel(title_frame, text="Administrator Required",
                                  font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(side="left", anchor="w")

        # Description
        desc_text = ("This application needs administrator privileges to write files "
                    "to the Steam folder.\n\n"
                    "Without administrator privileges, file downloads may fail with "
                    "permission errors.")

        desc_label = ctk.CTkLabel(main_frame, text=desc_text,
                                 font=ctk.CTkFont(size=14),
                                 wraplength=450, justify="left")
        desc_label.pack(fill="x", pady=(0, 20))

        # Instructions
        instructions_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        instructions_frame.pack(fill="x", pady=(0, 20))

        instructions_title = ctk.CTkLabel(instructions_frame,
                                        text="To restart as administrator:",
                                        font=ctk.CTkFont(size=16, weight="bold"))
        instructions_title.pack(anchor="w", padx=15, pady=(15, 5))

        steps = [
            "1. Close this application",
            "2. Right-click on the application file",
            "3. Select 'Run as administrator'",
            "4. Click 'Yes' when Windows asks for permission"
        ]

        for step in steps:
            step_label = ctk.CTkLabel(instructions_frame, text=step,
                                    font=ctk.CTkFont(size=14),
                                    anchor="w")
            step_label.pack(anchor="w", padx=25, pady=2)

        # Add bottom padding to instructions
        ctk.CTkLabel(instructions_frame, text="").pack(pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", pady=(10, 0))

        # Continue button
        continue_btn = ctk.CTkButton(button_frame, text="Continue Anyway",
                                   command=self.continue_clicked,
                                   width=140, height=35,
                                   fg_color="#ff6b35", hover_color="#e55a2b")
        continue_btn.pack(side="right", padx=(10, 0))

        # Restart button
        restart_btn = ctk.CTkButton(button_frame, text="Restart as Admin",
                                  command=self.restart_clicked,
                                  width=140, height=35)
        restart_btn.pack(side="right")

    def restart_clicked(self):
        """Handle restart as admin button click"""
        self.result = "restart"
        self.dialog.destroy()

    def continue_clicked(self):
        """Handle continue anyway button click"""
        self.result = "continue"
        self.dialog.destroy()

    def show(self):
        """Show the dialog and return the result"""
        self.dialog.wait_window()
        return self.result

class AdminLogger:
    """Comprehensive logging system for admin troubleshooting - hidden from customers"""

    def __init__(self):
        self.log_entries = []
        self.session_id = str(uuid.uuid4())[:8]
        self.start_time = datetime.now()
        self.system_info = self._capture_system_info()
        self.max_entries = 10000  # Prevent memory issues

        # Log session start
        self.log("INFO", "SYSTEM", "Admin logging session started", {
            "session_id": self.session_id,
            "system_info": self.system_info
        })

    def _capture_system_info(self):
        """Capture comprehensive system information"""
        try:
            return {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "memory_total": psutil.virtual_memory().total,
                "memory_available": psutil.virtual_memory().available,
                "disk_usage": dict(psutil.disk_usage('C:')._asdict()) if os.path.exists('C:') else {},
                "cpu_count": psutil.cpu_count(),
                "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                "current_user": os.getenv('USERNAME', 'Unknown'),
                "computer_name": os.getenv('COMPUTERNAME', 'Unknown')
            }
        except Exception as e:
            return {"error": f"Failed to capture system info: {str(e)}"}

    def log(self, level, category, message, details=None):
        """Add comprehensive log entry"""
        try:
            # Get caller information
            frame = inspect.currentframe().f_back
            caller_info = {
                "function": frame.f_code.co_name,
                "filename": os.path.basename(frame.f_code.co_filename),
                "line": frame.f_lineno
            }

            entry = {
                "timestamp": datetime.now().isoformat(),
                "level": level,
                "category": category,
                "message": message,
                "details": details or {},
                "caller": caller_info,
                "session_id": self.session_id
            }

            self.log_entries.append(entry)

            # Prevent memory overflow
            if len(self.log_entries) > self.max_entries:
                self.log_entries = self.log_entries[-self.max_entries//2:]

        except Exception as e:
            # Fallback logging to prevent infinite loops
            print(f"AdminLogger error: {e}")

    def log_exception(self, category, message, exception):
        """Log exception with full stack trace"""
        self.log("ERROR", category, message, {
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "stack_trace": traceback.format_exc()
        })

    def log_variable_change(self, var_name, old_value, new_value, context=""):
        """Log variable changes for debugging"""
        self.log("DEBUG", "VARIABLE", f"Variable '{var_name}' changed", {
            "variable_name": var_name,
            "old_value": str(old_value)[:500],  # Limit length
            "new_value": str(new_value)[:500],
            "context": context
        })

    def log_keyauth_activity(self, action, details):
        """Log keyauth-specific activities"""
        self.log("INFO", "KEYAUTH", f"Keyauth {action}", details)

    def log_download_activity(self, action, details):
        """Log download-specific activities"""
        self.log("INFO", "DOWNLOAD", f"Download {action}", details)

    def log_user_activity(self, action, details):
        """Log user interactions"""
        self.log("INFO", "USER", f"User {action}", details)

    def get_logs(self, level_filter=None, category_filter=None, search_term=None):
        """Get filtered logs"""
        filtered_logs = self.log_entries

        if level_filter:
            filtered_logs = [log for log in filtered_logs if log["level"] == level_filter]

        if category_filter:
            filtered_logs = [log for log in filtered_logs if log["category"] == category_filter]

        if search_term:
            search_term = search_term.lower()
            filtered_logs = [log for log in filtered_logs
                           if search_term in log["message"].lower() or
                              search_term in str(log["details"]).lower()]

        return filtered_logs

    def get_session_summary(self):
        """Get session summary information"""
        return {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "duration": str(datetime.now() - self.start_time),
            "total_logs": len(self.log_entries),
            "log_levels": {level: len([l for l in self.log_entries if l["level"] == level])
                          for level in ["INFO", "WARNING", "ERROR", "DEBUG"]},
            "categories": {cat: len([l for l in self.log_entries if l["category"] == cat])
                          for cat in set(l["category"] for l in self.log_entries)},
            "system_info": self.system_info
        }

    def clear_logs(self):
        """Clear all logs"""
        self.log_entries = []
        self.log("INFO", "SYSTEM", "Logs cleared by admin")

    def export_logs(self):
        """Export logs as formatted text"""
        lines = [
            "=" * 80,
            f"STEAM TOOLS ADMIN LOGS - SESSION {self.session_id}",
            f"Generated: {datetime.now().isoformat()}",
            f"Session Duration: {datetime.now() - self.start_time}",
            "=" * 80,
            "",
            "SYSTEM INFORMATION:",
            json.dumps(self.system_info, indent=2),
            "",
            "=" * 80,
            "LOG ENTRIES:",
            "=" * 80,
            ""
        ]

        for entry in self.log_entries:
            lines.extend([
                f"[{entry['timestamp']}] {entry['level']} - {entry['category']}",
                f"Message: {entry['message']}",
                f"Caller: {entry['caller']['function']}() at {entry['caller']['filename']}:{entry['caller']['line']}",
                f"Details: {json.dumps(entry['details'], indent=2)}",
                "-" * 40,
                ""
            ])

        return "\n".join(lines)

class ConfigManager:
    """Manages application configuration and settings"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            else:
                return self.get_default_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """Get default configuration"""
        return {
            "steam_path": "",
            "license_key_history": [],
            "admin_settings": {
                "password_hash": "240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9",  # admin123
                "auto_detect_steam": True,
                "remember_license_keys": True,
                "max_history_entries": 50
            },
            "app_cache": {},
            "last_used": {
                "license_key": "",
                "app_info": {}
            }
        }
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key, default=None):
        """Get configuration value"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set configuration value"""
        self.config[key] = value
        self.save_config()
    
    def add_license_history(self, license_key, app_info, success):
        """Add license key to history"""
        history_entry = {
            "key": license_key,
            "app_name": app_info.get("app_name", "Unknown"),
            "app_id": app_info.get("app_id", ""),
            "license_prefix": app_info.get("license_prefix", ""),
            "timestamp": datetime.now().isoformat(),
            "success": success
        }
        
        if "license_key_history" not in self.config:
            self.config["license_key_history"] = []
        
        self.config["license_key_history"].insert(0, history_entry)
        
        # Limit history entries
        max_entries = self.config.get("admin_settings", {}).get("max_history_entries", 50)
        if len(self.config["license_key_history"]) > max_entries:
            self.config["license_key_history"] = self.config["license_key_history"][:max_entries]
        
        self.save_config()
    
    def reset_all_data(self):
        """Reset all configuration data"""
        self.config = self.get_default_config()
        self.save_config()

class PasswordManager:
    """Manages admin password hashing and verification"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
    
    def hash_password(self, password):
        """Hash password using SHA256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password):
        """Verify password against stored hash"""
        password_hash = self.hash_password(password)
        stored_hash = self.config_manager.config.get("admin_settings", {}).get("password_hash", "")
        return password_hash == stored_hash
    
    def change_password(self, new_password):
        """Change admin password"""
        new_hash = self.hash_password(new_password)
        if "admin_settings" not in self.config_manager.config:
            self.config_manager.config["admin_settings"] = {}
        self.config_manager.config["admin_settings"]["password_hash"] = new_hash
        self.config_manager.save_config()

class ModernSteamToolsGUI:
    def __init__(self):
        # Initialize admin logger first
        self.admin_logger = AdminLogger()
        self.admin_logger.log("INFO", "SYSTEM", "Application starting", {
            "app_version": "2.0",
            "gui_framework": "customtkinter"
        })

        # Check administrator privileges at startup
        self.is_admin = is_admin()
        self.admin_logger.log("INFO", "SYSTEM", "Administrator privilege check", {
            "is_admin": self.is_admin,
            "check_time": datetime.now().isoformat()
        })

        # Initialize main window
        self.root = ctk.CTk()
        self.root.title("Steam Tools Downloader v2")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # Initialize configuration and password managers
        self.config_manager = ConfigManager()
        self.password_manager = PasswordManager(self.config_manager)

        # Initialize variables with logging
        self.steam_path = self.config_manager.get("steam_path", "")
        self.admin_logger.log_variable_change("steam_path", "", self.steam_path, "Initial load from config")

        self.keyauth_app = None
        self.license_key = ""
        self.app_info = {}
        self.is_admin_mode = False

        self.admin_logger.log("INFO", "SYSTEM", "GUI components initialized", {
            "window_size": "1000x700",
            "steam_path_configured": bool(self.steam_path)
        })
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-Shift-A>', self.toggle_admin_mode)
        self.root.bind('<Control-Shift-C>', self.switch_to_customer_mode)
        
        # Create GUI based on mode
        self.create_customer_mode()

        # Create status bar
        self.create_status_bar()

        # Initialize Keyauth after GUI is created (in a separate thread to avoid blocking)
        threading.Thread(target=self.init_keyauth, daemon=True).start()

        # Auto-detect Steam path if not set
        if not self.steam_path:
            self.auto_detect_steam_path()

        # Show admin warning if not running as admin
        if not self.is_admin:
            self.root.after(1000, self.show_admin_warning)  # Show after GUI is fully loaded
    
    def toggle_admin_mode(self, event=None):
        """Toggle between customer and admin mode with password verification"""
        self.admin_logger.log_user_activity("attempted mode toggle", {
            "current_mode": "admin" if self.is_admin_mode else "customer",
            "trigger": "keyboard shortcut" if event else "manual"
        })

        if self.is_admin_mode:
            self.switch_to_customer_mode()
        else:
            self.switch_to_admin_mode()

    def switch_to_admin_mode(self):
        """Switch to admin mode with password verification"""
        self.admin_logger.log_user_activity("admin access attempt", {
            "timestamp": datetime.now().isoformat()
        })

        password = simpledialog.askstring("Admin Access", "Enter admin password:", show='*')

        if password and self.password_manager.verify_password(password):
            self.admin_logger.log_user_activity("admin access granted", {
                "password_length": len(password),
                "success": True
            })

            old_mode = self.is_admin_mode
            self.is_admin_mode = True
            self.admin_logger.log_variable_change("is_admin_mode", old_mode, True, "Admin access granted")

            self.clear_widgets()
            self.create_admin_mode()
            self.log_message("Switched to Admin Mode")

        elif password:  # Password was entered but incorrect
            self.admin_logger.log_user_activity("admin access denied", {
                "password_length": len(password),
                "success": False,
                "reason": "incorrect_password"
            })
            messagebox.showerror("Access Denied", "Incorrect admin password")
        else:
            self.admin_logger.log_user_activity("admin access cancelled", {
                "reason": "no_password_entered"
            })

    def switch_to_customer_mode(self, event=None):
        """Switch to customer mode"""
        self.admin_logger.log_user_activity("switched to customer mode", {
            "previous_mode": "admin" if self.is_admin_mode else "customer",
            "trigger": "keyboard shortcut" if event else "manual"
        })

        old_mode = self.is_admin_mode
        self.is_admin_mode = False
        self.admin_logger.log_variable_change("is_admin_mode", old_mode, False, "Switched to customer mode")

        self.clear_widgets()
        self.create_customer_mode()
        if hasattr(self, 'log_message'):
            self.log_message("Switched to Customer Mode")
    
    def clear_widgets(self):
        """Clear all widgets from the window"""
        for widget in self.root.winfo_children():
            widget.destroy()
    
    def create_customer_mode(self):
        """Create modern customer interface"""
        # Main container
        main_frame = ctk.CTkFrame(self.root, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)
        
        # Header section
        header_frame = ctk.CTkFrame(main_frame, corner_radius=15, height=120)
        header_frame.pack(fill="x", pady=(0, 30))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = ctk.CTkLabel(header_frame, text="Steam Tools Downloader", 
                                  font=ctk.CTkFont(size=32, weight="bold"))
        title_label.pack(pady=(25, 5))
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(header_frame, 
                                     text="Enter your license key to automatically download and install files",
                                     font=ctk.CTkFont(size=14),
                                     text_color=("gray60", "gray40"))
        subtitle_label.pack()
        
        # License Key Card
        license_card = ctk.CTkFrame(main_frame, corner_radius=15)
        license_card.pack(fill="x", pady=(0, 20))
        
        # License card content
        license_content = ctk.CTkFrame(license_card, fg_color="transparent")
        license_content.pack(fill="x", padx=30, pady=30)
        
        license_label = ctk.CTkLabel(license_content, text="License Key", 
                                    font=ctk.CTkFont(size=16, weight="bold"))
        license_label.pack(anchor="w", pady=(0, 10))
        
        self.license_entry = ctk.CTkEntry(license_content, height=45, 
                                         font=ctk.CTkFont(size=14),
                                         placeholder_text="Enter your license key here...")
        self.license_entry.pack(fill="x", pady=(0, 20))
        
        # Download button
        self.auto_download_btn = ctk.CTkButton(license_content, text="Download & Install", 
                                              height=50, font=ctk.CTkFont(size=16, weight="bold"),
                                              command=self.auto_download)
        self.auto_download_btn.pack(fill="x")
        
        # Progress Card
        progress_card = ctk.CTkFrame(main_frame, corner_radius=15)
        progress_card.pack(fill="x", pady=(0, 20))
        
        progress_content = ctk.CTkFrame(progress_card, fg_color="transparent")
        progress_content.pack(fill="x", padx=30, pady=30)
        
        progress_label = ctk.CTkLabel(progress_content, text="Progress", 
                                     font=ctk.CTkFont(size=16, weight="bold"))
        progress_label.pack(anchor="w", pady=(0, 10))
        
        self.progress_var = ctk.StringVar(value="Ready to download")
        self.progress_status = ctk.CTkLabel(progress_content, textvariable=self.progress_var,
                                           font=ctk.CTkFont(size=14))
        self.progress_status.pack(anchor="w", pady=(0, 15))
        
        self.progress_bar = ctk.CTkProgressBar(progress_content, height=8)
        self.progress_bar.pack(fill="x", pady=(0, 10))
        self.progress_bar.set(0)
        
        # Status Log Card
        log_card = ctk.CTkFrame(main_frame, corner_radius=15)
        log_card.pack(fill="both", expand=True)
        
        log_content = ctk.CTkFrame(log_card, fg_color="transparent")
        log_content.pack(fill="both", expand=True, padx=30, pady=30)
        
        log_label = ctk.CTkLabel(log_content, text="Status Log", 
                                font=ctk.CTkFont(size=16, weight="bold"))
        log_label.pack(anchor="w", pady=(0, 10))
        
        self.status_text = ctk.CTkTextbox(log_content, height=200, 
                                         font=ctk.CTkFont(family="Consolas", size=12))
        self.status_text.pack(fill="both", expand=True)
        
        # Footer
        footer_frame = ctk.CTkFrame(main_frame, fg_color="transparent", height=40)
        footer_frame.pack(fill="x", pady=(20, 0))
        footer_frame.pack_propagate(False)
        
        hint_label = ctk.CTkLabel(footer_frame, text="Press Ctrl+Shift+A for admin mode", 
                                 font=ctk.CTkFont(size=12),
                                 text_color=("gray60", "gray40"))
        hint_label.pack()
        
        # Set focus to license entry
        self.license_entry.focus()
        
        # Bind Enter key to auto-download
        self.license_entry.bind('<Return>', lambda e: self.auto_download())

    def create_status_bar(self):
        """Create status bar showing admin and Steam path status"""
        self.status_frame = ctk.CTkFrame(self.root, height=40, corner_radius=0)
        self.status_frame.pack(side="bottom", fill="x")
        self.status_frame.pack_propagate(False)

        # Admin status
        admin_color = "#4CAF50" if self.is_admin else "#FF9800"
        admin_text = "Administrator: ✅ Yes" if self.is_admin else "Administrator: ⚠️ No"

        self.admin_status_label = ctk.CTkLabel(self.status_frame, text=admin_text,
                                              font=ctk.CTkFont(size=12, weight="bold"),
                                              text_color=admin_color)
        self.admin_status_label.pack(side="left", padx=15, pady=8)

        # Separator
        separator = ctk.CTkLabel(self.status_frame, text="•",
                               font=ctk.CTkFont(size=12),
                               text_color=("gray60", "gray40"))
        separator.pack(side="left", padx=5)

        # Steam path status
        steam_color = "#4CAF50" if self.steam_path else "#F44336"
        steam_text = f"Steam: ✅ {self.steam_path}" if self.steam_path else "Steam: ❌ Not Found"

        self.steam_status_label = ctk.CTkLabel(self.status_frame, text=steam_text,
                                              font=ctk.CTkFont(size=12),
                                              text_color=steam_color)
        self.steam_status_label.pack(side="left", padx=5, pady=8)

        self.admin_logger.log("INFO", "GUI", "Status bar created", {
            "admin_status": self.is_admin,
            "steam_path_status": bool(self.steam_path)
        })

    def update_status_bar(self):
        """Update status bar information"""
        if hasattr(self, 'admin_status_label'):
            # Update admin status
            admin_color = "#4CAF50" if self.is_admin else "#FF9800"
            admin_text = "Administrator: ✅ Yes" if self.is_admin else "Administrator: ⚠️ No"
            self.admin_status_label.configure(text=admin_text, text_color=admin_color)

            # Update Steam path status
            steam_color = "#4CAF50" if self.steam_path else "#F44336"
            steam_text = f"Steam: ✅ {self.steam_path}" if self.steam_path else "Steam: ❌ Not Found"
            self.steam_status_label.configure(text=steam_text, text_color=steam_color)

    def show_admin_warning(self):
        """Show custom admin warning dialog"""
        self.admin_logger.log("INFO", "SYSTEM", "Showing admin warning dialog", {
            "is_admin": self.is_admin,
            "reason": "not_running_as_admin"
        })

        dialog = AdminWarningDialog(self.root)
        result = dialog.show()

        self.admin_logger.log("INFO", "SYSTEM", "Admin warning dialog result", {
            "user_choice": result
        })

        if result == "restart":
            self.admin_logger.log("INFO", "SYSTEM", "User chose to restart as admin")
            if run_as_admin():
                self.admin_logger.log("INFO", "SYSTEM", "Successfully requested admin restart")
                self.root.quit()  # Close current instance
            else:
                self.admin_logger.log("ERROR", "SYSTEM", "Failed to restart as administrator")
                self.log_message("❌ Failed to restart as administrator. Please manually run as admin.")
        elif result == "continue":
            self.admin_logger.log("INFO", "SYSTEM", "User chose to continue without admin privileges")
            self.log_message("⚠️ Continuing without admin privileges - downloads may fail.")

    def log_message(self, message):
        """Add message to log - both GUI and admin logger"""
        timestamp = time.strftime("%H:%M:%S")
        log_text = f"[{timestamp}] {message}\n"

        # Log to admin logger with more detail
        self.admin_logger.log("INFO", "GUI", f"User message: {message}", {
            "display_timestamp": timestamp,
            "current_mode": "admin" if self.is_admin_mode else "customer",
            "gui_ready": hasattr(self, 'status_text') or hasattr(self, 'log_text')
        })

        # Use appropriate text widget based on mode
        try:
            if hasattr(self, 'status_text') and self.status_text.winfo_exists():  # Customer mode
                self.status_text.insert("end", log_text)
                self.status_text.see("end")
                self.root.update_idletasks()
            elif hasattr(self, 'log_text') and self.log_text.winfo_exists():  # Admin mode
                self.log_text.insert("end", log_text)
                self.log_text.see("end")
                self.root.update_idletasks()
            else:
                # Fallback: print to console if GUI not ready
                print(log_text.strip())
                self.admin_logger.log("WARNING", "GUI", "GUI not ready, using console fallback", {
                    "message": message
                })
        except Exception as e:
            # Fallback: print to console if any error
            print(log_text.strip())
            self.admin_logger.log_exception("GUI", "Error displaying message in GUI", e)

    def auto_download(self):
        """Automatically validate license and download files"""
        license_key = self.license_entry.get().strip()

        self.admin_logger.log_user_activity("download initiated", {
            "license_key_length": len(license_key),
            "license_key_prefix": license_key[:10] + "..." if len(license_key) > 10 else license_key,
            "steam_path_configured": bool(self.steam_path),
            "keyauth_initialized": self.keyauth_app is not None
        })

        if not license_key:
            self.admin_logger.log("WARNING", "USER", "Download attempted with empty license key")
            messagebox.showerror("Error", "Please enter a license key")
            return

        # Store license key for logging
        old_license = self.license_key
        self.license_key = license_key
        self.admin_logger.log_variable_change("license_key", old_license, license_key, "User entered new license key")

        # Disable button during process
        self.auto_download_btn.configure(state='disabled')
        self.progress_bar.set(0)
        self.progress_var.set("Validating license...")

        # Clear status
        self.status_text.delete("1.0", "end")

        self.admin_logger.log("INFO", "DOWNLOAD", "Starting download thread", {
            "license_key_masked": license_key[:5] + "*" * (len(license_key) - 5) if len(license_key) > 5 else "*" * len(license_key)
        })

        # Run in separate thread
        threading.Thread(target=self._auto_download_thread, args=(license_key,), daemon=True).start()

    def _auto_download_thread(self, license_key):
        """Auto-download thread"""
        thread_start_time = datetime.now()
        self.admin_logger.log("INFO", "DOWNLOAD", "Download thread started", {
            "thread_start_time": thread_start_time.isoformat(),
            "license_key_masked": license_key[:5] + "*" * (len(license_key) - 5) if len(license_key) > 5 else "*" * len(license_key)
        })

        try:
            # Validate license
            self.root.after(0, lambda: self.log_message("Validating license key..."))

            self.admin_logger.log_keyauth_activity("license_validation_start", {
                "license_key_length": len(license_key),
                "license_key_prefix": license_key.split('-')[0] if '-' in license_key else license_key[:10],
                "keyauth_app_status": "initialized" if self.keyauth_app else "not_initialized"
            })

            if not self.keyauth_app:
                self.admin_logger.log("ERROR", "KEYAUTH", "Keyauth not initialized during license validation")
                self.root.after(0, lambda: self.log_message("❌ Keyauth not initialized"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Keyauth not initialized. Please wait and try again."))
                return

            validation_start = datetime.now()
            is_valid = self.keyauth_app.license(license_key)
            validation_duration = (datetime.now() - validation_start).total_seconds()

            self.admin_logger.log_keyauth_activity("license_validation_complete", {
                "is_valid": is_valid,
                "validation_duration_seconds": validation_duration,
                "license_key_masked": license_key[:5] + "*" * (len(license_key) - 5) if len(license_key) > 5 else "*" * len(license_key)
            })

            if not is_valid:
                self.admin_logger.log("WARNING", "KEYAUTH", "License validation failed", {
                    "license_key_prefix": license_key.split('-')[0] if '-' in license_key else license_key[:10],
                    "validation_duration": validation_duration
                })
                self.root.after(0, lambda: self.log_message("❌ Invalid license key"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Invalid license key"))
                return

            self.root.after(0, lambda: self.log_message("✅ License key validated"))

            # Get app information
            self.admin_logger.log_keyauth_activity("fetching_app_data", {
                "variable_name": "Main"
            })

            main_data = self.keyauth_app.var("Main")

            if not main_data:
                self.admin_logger.log("ERROR", "KEYAUTH", "Failed to retrieve app data from keyauth variable 'Main'")
                self.root.after(0, lambda: self.log_message("❌ Failed to get app data"))
                return

            self.admin_logger.log_keyauth_activity("app_data_received", {
                "data_length": len(main_data),
                "data_preview": main_data[:200] + "..." if len(main_data) > 200 else main_data
            })

            try:
                apps_data = json.loads(main_data)
                self.admin_logger.log("INFO", "KEYAUTH", "App data parsed successfully", {
                    "apps_count": len(apps_data.get("apps", [])),
                    "apps_available": [app.get("license_prefix", "unknown") for app in apps_data.get("apps", [])]
                })
            except json.JSONDecodeError as e:
                self.admin_logger.log_exception("KEYAUTH", "Failed to parse app data JSON", e)
                self.root.after(0, lambda: self.log_message("❌ Invalid app data format"))
                return

            app_info = None

            # Find matching app
            license_prefix = license_key.split('-')[0] + '-' if '-' in license_key else license_key[:10]
            self.admin_logger.log("INFO", "KEYAUTH", "Searching for matching app", {
                "license_prefix": license_prefix,
                "available_prefixes": [app.get("license_prefix", "unknown") for app in apps_data.get("apps", [])]
            })

            for app in apps_data["apps"]:
                if license_key.startswith(app["license_prefix"]):
                    app_info = app
                    self.admin_logger.log("INFO", "KEYAUTH", "Found matching app", {
                        "app_name": app.get("app_name", "unknown"),
                        "app_id": app.get("app_id", "unknown"),
                        "license_prefix": app.get("license_prefix", "unknown"),
                        "complete_app_structure": app,
                        "all_app_keys": list(app.keys())
                    })
                    break

            if not app_info:
                self.admin_logger.log("ERROR", "KEYAUTH", "No matching app found for license key", {
                    "license_prefix": license_prefix,
                    "available_apps": [{"name": app.get("app_name"), "prefix": app.get("license_prefix")}
                                     for app in apps_data.get("apps", [])]
                })
                self.root.after(0, lambda: self.log_message("❌ No matching app found"))
                return

            # Validate app_info structure - only need basic info now
            required_keys = ["app_id", "app_name"]
            missing_keys = [key for key in required_keys if key not in app_info]

            if missing_keys:
                self.admin_logger.log("ERROR", "KEYAUTH", "App info missing required keys", {
                    "missing_keys": missing_keys,
                    "available_keys": list(app_info.keys()),
                    "app_info_structure": app_info
                })
                self.root.after(0, lambda: self.log_message(f"❌ App data incomplete. Missing: {', '.join(missing_keys)}"))
                return

            # Store app info for logging
            old_app_info = self.app_info
            self.app_info = app_info
            self.admin_logger.log_variable_change("app_info", str(old_app_info), str(app_info), "Found matching app")

            self.root.after(0, lambda: self.log_message(f"📱 Found app: {app_info['app_name']}"))

            # Ensure Steam path is available
            if not self.steam_path:
                self.root.after(0, lambda: self.auto_detect_steam_path())
                if not self.steam_path:
                    self.root.after(0, lambda: self.log_message("❌ Steam path not found"))
                    self.root.after(0, lambda: messagebox.showerror("Error", "Steam installation not found. Please contact admin."))
                    return

            # Start download process
            self.root.after(0, lambda: self.progress_var.set("Downloading files..."))
            success = self._download_all_files(app_info)

            # Add to history
            self.config_manager.add_license_history(license_key, app_info, success)

            if success:
                self.root.after(0, lambda: self.progress_var.set("✅ Installation completed!"))
                self.root.after(0, lambda: self.log_message("🎉 All files downloaded and installed successfully!"))
                self.root.after(0, lambda: messagebox.showinfo("Success", "Files downloaded and installed successfully!"))
            else:
                self.root.after(0, lambda: self.progress_var.set("❌ Installation failed"))
                self.root.after(0, lambda: messagebox.showerror("Error", "Some files failed to download. Please try again."))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ Error: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"An error occurred: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.auto_download_btn.configure(state='normal'))

    def init_keyauth(self):
        """Initialize Keyauth API"""
        init_start_time = datetime.now()
        checksum = self.get_checksum()

        self.admin_logger.log_keyauth_activity("initialization_start", {
            "app_name": "MainSteam",
            "owner_id": "1tGVnUKtzH",
            "version": "1.0",
            "file_checksum": checksum,
            "init_start_time": init_start_time.isoformat()
        })

        try:
            self.keyauth_app = api(
                name="MainSteam",
                ownerid="1tGVnUKtzH",
                secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
                version="1.0",
                hash_to_check=checksum
            )

            init_duration = (datetime.now() - init_start_time).total_seconds()
            self.admin_logger.log_keyauth_activity("initialization_success", {
                "duration_seconds": init_duration,
                "keyauth_app_created": self.keyauth_app is not None
            })

            self.log_message("Keyauth initialized successfully")

        except Exception as e:
            init_duration = (datetime.now() - init_start_time).total_seconds()
            self.admin_logger.log_exception("KEYAUTH", "Keyauth initialization failed", e)
            self.admin_logger.log_keyauth_activity("initialization_failed", {
                "duration_seconds": init_duration,
                "error_message": str(e)
            })

            # Show user-friendly error message
            self.log_message("❌ Something unexpected happened during initialization.")
            self.log_message("Please try again later or contact support if this continues.")
            messagebox.showerror("Error", "Something unexpected happened. Please try again later or contact support if this continues.")
            self.keyauth_app = None

    def get_checksum(self):
        """Get file checksum for Keyauth"""
        try:
            md5_hash = hashlib.md5()
            with open(__file__, "rb") as f:
                md5_hash.update(f.read())
            return md5_hash.hexdigest()
        except:
            return ""

    def auto_detect_steam_path(self):
        """Auto-detect Steam installation path from registry"""
        detection_start = datetime.now()
        self.admin_logger.log("INFO", "SYSTEM", "Starting Steam path auto-detection", {
            "detection_start_time": detection_start.isoformat()
        })

        try:
            # Try to get Steam path from registry
            self.admin_logger.log("INFO", "SYSTEM", "Attempting registry lookup: HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Valve\\Steam")

            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "InstallPath")[0]

                self.admin_logger.log("INFO", "SYSTEM", "Registry path found", {
                    "registry_path": steam_path,
                    "path_exists": os.path.exists(steam_path)
                })

                if os.path.exists(steam_path):
                    old_steam_path = self.steam_path
                    self.steam_path = steam_path
                    self.admin_logger.log_variable_change("steam_path", old_steam_path, steam_path, "Auto-detected from registry HKLM")

                    self.config_manager.set("steam_path", steam_path)
                    self.log_message(f"Steam path detected: {steam_path}")

                    if hasattr(self, 'steam_path_var'):
                        self.steam_path_var.set(steam_path)

                    # Update status bar
                    self.update_status_bar()

                    self.admin_logger.log("INFO", "SYSTEM", "Steam path auto-detection successful", {
                        "method": "HKLM_registry",
                        "path": steam_path,
                        "detection_duration": (datetime.now() - detection_start).total_seconds()
                    })
                    return True

        except Exception as e:
            self.admin_logger.log("INFO", "SYSTEM", "HKLM registry lookup failed", {
                "error": str(e),
                "error_type": type(e).__name__
            })

        # Try alternative registry location
        try:
            self.admin_logger.log("INFO", "SYSTEM", "Attempting registry lookup: HKEY_CURRENT_USER\\SOFTWARE\\Valve\\Steam")

            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "SteamPath")[0]

                self.admin_logger.log("INFO", "SYSTEM", "HKCU registry path found", {
                    "registry_path": steam_path,
                    "path_exists": os.path.exists(steam_path)
                })

                if os.path.exists(steam_path):
                    old_steam_path = self.steam_path
                    self.steam_path = steam_path
                    self.admin_logger.log_variable_change("steam_path", old_steam_path, steam_path, "Auto-detected from registry HKCU")

                    self.config_manager.set("steam_path", steam_path)
                    self.log_message(f"Steam path detected: {steam_path}")

                    if hasattr(self, 'steam_path_var'):
                        self.steam_path_var.set(steam_path)

                    # Update status bar
                    self.update_status_bar()

                    self.admin_logger.log("INFO", "SYSTEM", "Steam path auto-detection successful", {
                        "method": "HKCU_registry",
                        "path": steam_path,
                        "detection_duration": (datetime.now() - detection_start).total_seconds()
                    })
                    return True

        except Exception as e:
            self.admin_logger.log("INFO", "SYSTEM", "HKCU registry lookup failed", {
                "error": str(e),
                "error_type": type(e).__name__
            })

        # Try common installation paths
        common_paths = [
            r"C:\Program Files (x86)\Steam",
            r"C:\Program Files\Steam",
            r"D:\Steam",
            r"E:\Steam"
        ]

        self.admin_logger.log("INFO", "SYSTEM", "Trying common installation paths", {
            "paths_to_check": common_paths
        })

        for path in common_paths:
            self.admin_logger.log("DEBUG", "SYSTEM", f"Checking path: {path}", {
                "path_exists": os.path.exists(path),
                "steam_exe_exists": os.path.exists(os.path.join(path, "steam.exe")) if os.path.exists(path) else False
            })

            if os.path.exists(path) and os.path.exists(os.path.join(path, "steam.exe")):
                old_steam_path = self.steam_path
                self.steam_path = path
                self.admin_logger.log_variable_change("steam_path", old_steam_path, path, "Auto-detected from common paths")

                self.config_manager.set("steam_path", path)
                self.log_message(f"Steam path found: {path}")

                if hasattr(self, 'steam_path_var'):
                    self.steam_path_var.set(path)

                self.admin_logger.log("INFO", "SYSTEM", "Steam path auto-detection successful", {
                    "method": "common_path",
                    "path": path,
                    "detection_duration": (datetime.now() - detection_start).total_seconds()
                })
                return True

        self.admin_logger.log("WARNING", "SYSTEM", "Steam path auto-detection failed", {
            "detection_duration": (datetime.now() - detection_start).total_seconds(),
            "methods_tried": ["HKLM_registry", "HKCU_registry", "common_paths"],
            "common_paths_checked": common_paths
        })

        self.log_message("Steam installation not found automatically")
        return False

    def _download_all_files(self, app_info):
        """Download all required files for the app"""
        download_start_time = datetime.now()

        self.admin_logger.log_download_activity("download_session_start", {
            "app_info": app_info,
            "steam_path": self.steam_path,
            "download_start_time": download_start_time.isoformat()
        })



        # Validate app_info structure before proceeding
        required_keys = ["app_id"]
        missing_keys = [key for key in required_keys if key not in app_info]

        if missing_keys:
            self.admin_logger.log("ERROR", "DOWNLOAD", "Cannot download - app_info missing required keys", {
                "missing_keys": missing_keys,
                "available_keys": list(app_info.keys()),
                "app_info_structure": app_info
            })
            self.log_message(f"❌ Cannot download: Missing {', '.join(missing_keys)} in app data")
            return False

        try:
            total_files = 4
            current_file = 0
            success_count = 0

            # Create stplug-in directory
            stplug_in_path = os.path.join(self.steam_path, "config", "stplug-in")

            self.admin_logger.log("INFO", "SYSTEM", "Creating stplug-in directory", {
                "directory_path": stplug_in_path,
                "directory_exists": os.path.exists(stplug_in_path)
            })

            os.makedirs(stplug_in_path, exist_ok=True)

            self.admin_logger.log("INFO", "SYSTEM", "Directory created/verified", {
                "directory_path": stplug_in_path,
                "directory_exists_after": os.path.exists(stplug_in_path)
            })

            self.log_message(f"📁 Created/verified directory: {stplug_in_path}")

            # Get download URLs from keyauth variables with proper error handling
            # These should be set in your keyauth panel as variables
            hid_dll_url = None
            luapacka_url = None
            steamtools_url = None
            lua_script_url = None

            try:
                self.admin_logger.log("INFO", "KEYAUTH", "Attempting to retrieve download URLs from keyauth variables")

                # Try to get each URL individually with error handling using correct variable names
                try:
                    hid_dll_url = self.keyauth_app.var("SteamTools")
                    self.admin_logger.log("INFO", "KEYAUTH", "Retrieved SteamTools (hid.dll)", {"url": hid_dll_url})
                except Exception as e:
                    self.admin_logger.log("WARNING", "KEYAUTH", "SteamTools variable not found", {"error": str(e)})
                    hid_dll_url = None

                try:
                    luapacka_url = self.keyauth_app.var("SteamTools-File1")
                    self.admin_logger.log("INFO", "KEYAUTH", "Retrieved SteamTools-File1 (luapacka.exe)", {"url": luapacka_url})
                except Exception as e:
                    self.admin_logger.log("WARNING", "KEYAUTH", "SteamTools-File1 variable not found", {"error": str(e)})
                    luapacka_url = None

                try:
                    steamtools_url = self.keyauth_app.var("SteamTools-File2")
                    self.admin_logger.log("INFO", "KEYAUTH", "Retrieved SteamTools-File2 (Steamtools.st)", {"url": steamtools_url})
                except Exception as e:
                    self.admin_logger.log("WARNING", "KEYAUTH", "SteamTools-File2 variable not found", {"error": str(e)})
                    steamtools_url = None

                try:
                    # Use app name as variable name for the lua script
                    app_name = app_info.get("app_name", "")
                    lua_script_url = self.keyauth_app.var(app_name)
                    self.admin_logger.log("INFO", "KEYAUTH", f"Retrieved {app_name} (lua script)", {"url": lua_script_url})
                except Exception as e:
                    self.admin_logger.log("WARNING", "KEYAUTH", f"{app_name} variable not found", {"error": str(e)})
                    lua_script_url = None

                # Check if any URLs were found
                urls_found = [url for url in [hid_dll_url, luapacka_url, steamtools_url, lua_script_url] if url]

                if not urls_found:
                    app_name = app_info.get("app_name", "Unknown")
                    self.admin_logger.log("ERROR", "KEYAUTH", "No download URLs found in keyauth variables", {
                        "variables_checked": ["SteamTools", "SteamTools-File1", "SteamTools-File2", app_name]
                    })
                    self.root.after(0, lambda: self.log_message("❌ Something unexpected happened. Please try again later."))
                    self.root.after(0, lambda: messagebox.showerror("Error", "Something unexpected happened. Please contact support if this continues."))
                    return False

                self.admin_logger.log("INFO", "KEYAUTH", "Successfully retrieved some download URLs", {
                    "urls_found": len(urls_found),
                    "total_expected": 4
                })

            except Exception as e:
                self.admin_logger.log_exception("KEYAUTH", "Critical error retrieving download URLs from keyauth", e)
                self.root.after(0, lambda: self.log_message("❌ Something unexpected happened. Please try again later."))
                self.root.after(0, lambda: messagebox.showerror("Error", "Something unexpected happened. Please contact support if this continues."))
                return False

            # File mappings - According to README.md (only include files with valid URLs)
            files_to_download = []

            if hid_dll_url:
                files_to_download.append({
                    "url": hid_dll_url,
                    "path": os.path.join(self.steam_path, "hid.dll"),
                    "name": "HID Library"
                })

            if luapacka_url:
                files_to_download.append({
                    "url": luapacka_url,
                    "path": os.path.join(stplug_in_path, "luapacka.exe"),
                    "name": "Lua Packer"
                })

            if steamtools_url:
                files_to_download.append({
                    "url": steamtools_url,
                    "path": os.path.join(stplug_in_path, "Steamtools.st"),
                    "name": "Steam Tools"
                })

            if lua_script_url:
                files_to_download.append({
                    "url": lua_script_url,
                    "path": os.path.join(stplug_in_path, f"{app_info['app_id']}.lua"),
                    "name": "Game Script"
                })

            if not files_to_download:
                self.admin_logger.log("ERROR", "DOWNLOAD", "No files to download - all URLs are missing", {
                    "hid_dll_url": bool(hid_dll_url),
                    "luapacka_url": bool(luapacka_url),
                    "steamtools_url": bool(steamtools_url),
                    "lua_script_url": bool(lua_script_url)
                })
                self.root.after(0, lambda: self.log_message("❌ Something unexpected happened. No files available for download."))
                self.root.after(0, lambda: messagebox.showerror("Error", "Something unexpected happened. Please contact support if this continues."))
                return False

            self.admin_logger.log_download_activity("files_mapped", {
                "total_files": len(files_to_download),
                "file_mappings": [{"name": f["name"], "url": f["url"], "path": f["path"]} for f in files_to_download]
            })

            # Create necessary directories
            directories_to_create = [
                os.path.join(self.steam_path, "appcache"),
                os.path.join(self.steam_path, "steamapps")
            ]

            for directory in directories_to_create:
                self.admin_logger.log("INFO", "SYSTEM", f"Creating directory: {directory}", {
                    "directory_exists": os.path.exists(directory)
                })
                os.makedirs(directory, exist_ok=True)
                self.admin_logger.log("INFO", "SYSTEM", f"Directory created/verified: {directory}", {
                    "directory_exists_after": os.path.exists(directory)
                })

            # Download each file
            for file_info in files_to_download:
                current_file += 1
                progress = current_file / total_files
                file_download_start = datetime.now()

                self.admin_logger.log_download_activity("file_download_start", {
                    "file_name": file_info["name"],
                    "file_url": file_info["url"],
                    "file_path": file_info["path"],
                    "file_number": current_file,
                    "total_files": total_files,
                    "progress_percent": progress * 100,
                    "download_start_time": file_download_start.isoformat()
                })

                self.root.after(0, lambda p=progress: self.progress_bar.set(p))
                self.root.after(0, lambda f=file_info["name"]: self.progress_var.set(f"Downloading {f}..."))

                self.log_message(f"📥 Downloading {file_info['name']}...")

                try:
                    # Make HTTP request with detailed logging
                    self.admin_logger.log_download_activity("http_request_start", {
                        "url": file_info["url"],
                        "timeout": 30,
                        "method": "GET"
                    })

                    response = requests.get(file_info["url"], timeout=30)

                    self.admin_logger.log_download_activity("http_response_received", {
                        "status_code": response.status_code,
                        "content_length": len(response.content),
                        "headers": dict(response.headers),
                        "url": file_info["url"]
                    })

                    response.raise_for_status()

                    # Ensure directory exists
                    file_directory = os.path.dirname(file_info["path"])
                    self.admin_logger.log("INFO", "SYSTEM", f"Ensuring directory exists: {file_directory}")
                    os.makedirs(file_directory, exist_ok=True)

                    # Write file with logging
                    self.admin_logger.log_download_activity("file_write_start", {
                        "file_path": file_info["path"],
                        "content_size": len(response.content)
                    })

                    with open(file_info["path"], 'wb') as f:
                        f.write(response.content)

                    # Verify file was written
                    file_size = os.path.getsize(file_info["path"]) if os.path.exists(file_info["path"]) else 0
                    download_duration = (datetime.now() - file_download_start).total_seconds()

                    self.admin_logger.log_download_activity("file_download_success", {
                        "file_name": file_info["name"],
                        "file_path": file_info["path"],
                        "file_size": file_size,
                        "download_duration_seconds": download_duration,
                        "download_speed_bytes_per_second": file_size / download_duration if download_duration > 0 else 0
                    })

                    self.log_message(f"✅ {file_info['name']} downloaded successfully")
                    success_count += 1

                except PermissionError as e:
                    download_duration = (datetime.now() - file_download_start).total_seconds()
                    self.admin_logger.log_exception("DOWNLOAD", f"Permission denied downloading {file_info['name']}", e)
                    self.admin_logger.log_download_activity("file_download_failed", {
                        "file_name": file_info["name"],
                        "file_url": file_info["url"],
                        "file_path": file_info["path"],
                        "error_message": str(e),
                        "error_type": "PermissionError",
                        "download_duration_seconds": download_duration
                    })
                    self.log_message(f"❌ Permission denied: Cannot write {file_info['name']} to Steam folder.")
                    self.log_message("💡 Please restart the application as administrator.")

                except Exception as e:
                    download_duration = (datetime.now() - file_download_start).total_seconds()
                    self.admin_logger.log_exception("DOWNLOAD", f"Failed to download {file_info['name']}", e)
                    self.admin_logger.log_download_activity("file_download_failed", {
                        "file_name": file_info["name"],
                        "file_url": file_info["url"],
                        "file_path": file_info["path"],
                        "error_message": str(e),
                        "download_duration_seconds": download_duration
                    })
                    self.log_message(f"❌ Failed to download {file_info['name']}: {str(e)}")

            # Final progress update
            self.root.after(0, lambda: self.progress_bar.set(1.0))

            download_duration = (datetime.now() - download_start_time).total_seconds()
            download_success = success_count == total_files

            self.admin_logger.log_download_activity("download_session_complete", {
                "total_files": total_files,
                "successful_downloads": success_count,
                "failed_downloads": total_files - success_count,
                "overall_success": download_success,
                "total_duration_seconds": download_duration,
                "app_info": app_info
            })

            return download_success

        except Exception as e:
            download_duration = (datetime.now() - download_start_time).total_seconds()
            self.admin_logger.log_exception("DOWNLOAD", "Download session failed with exception", e)
            self.admin_logger.log_download_activity("download_session_failed", {
                "error_message": str(e),
                "duration_seconds": download_duration,
                "files_attempted": current_file,
                "files_successful": success_count
            })
            self.log_message(f"❌ Download error: {str(e)}")
            return False

    def create_admin_mode(self):
        """Create modern admin interface"""
        # Main container with two columns
        main_frame = ctk.CTkFrame(self.root, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)

        # Header
        header_frame = ctk.CTkFrame(main_frame, corner_radius=15, height=80)
        header_frame.pack(fill="x", pady=(0, 30))
        header_frame.pack_propagate(False)

        title_label = ctk.CTkLabel(header_frame, text="Admin Panel",
                                  font=ctk.CTkFont(size=28, weight="bold"))
        title_label.pack(pady=25)

        # Content area with two columns
        content_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        content_frame.pack(fill="both", expand=True)

        # Left column - Management
        left_column = ctk.CTkFrame(content_frame, corner_radius=15)
        left_column.pack(side="left", fill="both", expand=True, padx=(0, 15))

        left_content = ctk.CTkFrame(left_column, fg_color="transparent")
        left_content.pack(fill="both", expand=True, padx=30, pady=30)

        # Management section
        mgmt_label = ctk.CTkLabel(left_content, text="System Management",
                                 font=ctk.CTkFont(size=18, weight="bold"))
        mgmt_label.pack(anchor="w", pady=(0, 20))

        # Steam path configuration
        steam_frame = ctk.CTkFrame(left_content, corner_radius=10)
        steam_frame.pack(fill="x", pady=(0, 15))

        steam_content = ctk.CTkFrame(steam_frame, fg_color="transparent")
        steam_content.pack(fill="x", padx=20, pady=20)

        steam_label = ctk.CTkLabel(steam_content, text="Steam Installation Path",
                                  font=ctk.CTkFont(size=14, weight="bold"))
        steam_label.pack(anchor="w", pady=(0, 10))

        self.steam_path_var = ctk.StringVar(value=self.steam_path if self.steam_path else "Not configured")
        steam_path_label = ctk.CTkLabel(steam_content, textvariable=self.steam_path_var,
                                       font=ctk.CTkFont(size=12),
                                       text_color=("gray60", "gray40"))
        steam_path_label.pack(anchor="w", pady=(0, 10))

        steam_btn_frame = ctk.CTkFrame(steam_content, fg_color="transparent")
        steam_btn_frame.pack(fill="x")

        ctk.CTkButton(steam_btn_frame, text="Browse", command=self.browse_steam_path,
                     height=35, width=100).pack(side="left", padx=(0, 10))
        ctk.CTkButton(steam_btn_frame, text="Auto-detect", command=self.auto_detect_steam_path,
                     height=35, width=100).pack(side="left")

        # Action buttons
        actions_frame = ctk.CTkFrame(left_content, corner_radius=10)
        actions_frame.pack(fill="x", pady=(0, 15))

        actions_content = ctk.CTkFrame(actions_frame, fg_color="transparent")
        actions_content.pack(fill="x", padx=20, pady=20)

        actions_label = ctk.CTkLabel(actions_content, text="Actions",
                                    font=ctk.CTkFont(size=14, weight="bold"))
        actions_label.pack(anchor="w", pady=(0, 15))

        # Button grid
        btn_grid = ctk.CTkFrame(actions_content, fg_color="transparent")
        btn_grid.pack(fill="x")

        ctk.CTkButton(btn_grid, text="View License History", command=self.view_license_history,
                     height=40).pack(fill="x", pady=(0, 10))
        ctk.CTkButton(btn_grid, text="View System Logs", command=self.view_system_logs,
                     height=40, fg_color=("purple", "darkviolet")).pack(fill="x", pady=(0, 10))
        ctk.CTkButton(btn_grid, text="Reset stplug-in Folder", command=self.reset_stplug_folder,
                     height=40, fg_color=("orange", "darkorange")).pack(fill="x", pady=(0, 10))
        ctk.CTkButton(btn_grid, text="Reset All Data", command=self.reset_all_data,
                     height=40, fg_color=("red", "darkred")).pack(fill="x", pady=(0, 10))
        ctk.CTkButton(btn_grid, text="Change Admin Password", command=self.change_admin_password,
                     height=40).pack(fill="x")

        # Right column - Activity Log
        right_column = ctk.CTkFrame(content_frame, corner_radius=15)
        right_column.pack(side="right", fill="both", expand=True, padx=(15, 0))

        right_content = ctk.CTkFrame(right_column, fg_color="transparent")
        right_content.pack(fill="both", expand=True, padx=30, pady=30)

        log_label = ctk.CTkLabel(right_content, text="Activity Log",
                                font=ctk.CTkFont(size=18, weight="bold"))
        log_label.pack(anchor="w", pady=(0, 20))

        self.log_text = ctk.CTkTextbox(right_content, font=ctk.CTkFont(family="Consolas", size=12))
        self.log_text.pack(fill="both", expand=True)

        # Footer
        footer_frame = ctk.CTkFrame(main_frame, fg_color="transparent", height=40)
        footer_frame.pack(fill="x", pady=(20, 0))
        footer_frame.pack_propagate(False)

        hint_label = ctk.CTkLabel(footer_frame, text="Press Ctrl+Shift+C to return to customer mode",
                                 font=ctk.CTkFont(size=12),
                                 text_color=("gray60", "gray40"))
        hint_label.pack()

    def browse_steam_path(self):
        """Browse for Steam installation path"""
        self.admin_logger.log_user_activity("browse steam path initiated", {})

        path = filedialog.askdirectory(title="Select Steam Installation Directory")

        self.admin_logger.log_user_activity("steam path browse result", {
            "path_selected": path if path else "cancelled",
            "path_exists": os.path.exists(path) if path else False,
            "steam_exe_exists": os.path.exists(os.path.join(path, "steam.exe")) if path else False
        })

        if path and os.path.exists(os.path.join(path, "steam.exe")):
            old_steam_path = self.steam_path
            self.steam_path = path
            self.admin_logger.log_variable_change("steam_path", old_steam_path, path, "Manually browsed and selected")

            self.config_manager.set("steam_path", path)
            self.steam_path_var.set(path)
            self.log_message(f"Steam path set to: {path}")

            # Update status bar
            self.update_status_bar()

            self.admin_logger.log_user_activity("steam path set successfully", {
                "new_path": path,
                "method": "manual_browse"
            })
        elif path:
            self.admin_logger.log("WARNING", "USER", "Invalid Steam directory selected", {
                "selected_path": path,
                "steam_exe_found": False
            })
            messagebox.showerror("Error", "Invalid Steam directory. steam.exe not found.")

    def reset_all_data(self):
        """Reset all application data"""
        self.admin_logger.log_user_activity("reset all data requested", {
            "current_steam_path": self.steam_path,
            "license_history_count": len(self.config_manager.config.get("license_key_history", []))
        })

        if messagebox.askyesno("Confirm Reset", "This will reset ALL application data including license history and settings. Continue?"):
            self.admin_logger.log_user_activity("reset all data confirmed", {})

            try:
                # Backup current state for logging
                old_config = self.config_manager.config.copy()

                self.config_manager.reset_all_data()
                old_steam_path = self.steam_path
                self.steam_path = ""
                self.admin_logger.log_variable_change("steam_path", old_steam_path, "", "Reset all data")

                if hasattr(self, 'steam_path_var'):
                    self.steam_path_var.set("Not configured")

                self.admin_logger.log_user_activity("reset all data completed", {
                    "old_config_keys": list(old_config.keys()),
                    "old_license_history_count": len(old_config.get("license_key_history", [])),
                    "reset_successful": True
                })

                self.log_message("✅ All data reset successfully")
                messagebox.showinfo("Success", "All data has been reset")

            except Exception as e:
                self.admin_logger.log_exception("SYSTEM", "Failed to reset all data", e)
                self.admin_logger.log_user_activity("reset all data failed", {
                    "error_message": str(e)
                })
                self.log_message(f"❌ Error resetting data: {str(e)}")
                messagebox.showerror("Error", f"Failed to reset data: {str(e)}")
        else:
            self.admin_logger.log_user_activity("reset all data cancelled", {})

    def reset_stplug_folder(self):
        """Reset/delete stplug-in folder"""
        self.admin_logger.log_user_activity("reset stplug folder requested", {
            "steam_path_configured": bool(self.steam_path),
            "steam_path": self.steam_path
        })

        if not self.steam_path:
            self.admin_logger.log("WARNING", "USER", "Reset stplug folder attempted without steam path")
            messagebox.showerror("Error", "Steam path not set")
            return

        stplug_path = os.path.join(self.steam_path, "config", "stplug-in")
        folder_exists = os.path.exists(stplug_path)
        folder_size = 0
        file_count = 0

        if folder_exists:
            try:
                for root, dirs, files in os.walk(stplug_path):
                    file_count += len(files)
                    for file in files:
                        folder_size += os.path.getsize(os.path.join(root, file))
            except:
                pass

        self.admin_logger.log_user_activity("stplug folder analysis", {
            "stplug_path": stplug_path,
            "folder_exists": folder_exists,
            "folder_size_bytes": folder_size,
            "file_count": file_count
        })

        if messagebox.askyesno("Confirm Reset", f"This will delete the stplug-in folder:\n{stplug_path}\n\nContinue?"):
            self.admin_logger.log_user_activity("reset stplug folder confirmed", {})

            try:
                if os.path.exists(stplug_path):
                    shutil.rmtree(stplug_path)

                    self.admin_logger.log_user_activity("stplug folder deleted", {
                        "folder_path": stplug_path,
                        "files_deleted": file_count,
                        "bytes_deleted": folder_size,
                        "success": True
                    })

                    self.log_message("✅ stplug-in folder deleted successfully")
                    messagebox.showinfo("Success", "stplug-in folder has been reset")
                else:
                    self.admin_logger.log_user_activity("stplug folder not found", {
                        "folder_path": stplug_path
                    })
                    self.log_message("ℹ️ stplug-in folder does not exist")
                    messagebox.showinfo("Info", "stplug-in folder does not exist")

            except Exception as e:
                self.admin_logger.log_exception("SYSTEM", "Failed to reset stplug-in folder", e)
                self.admin_logger.log_user_activity("stplug folder reset failed", {
                    "folder_path": stplug_path,
                    "error_message": str(e)
                })
                self.log_message(f"❌ Error resetting stplug-in folder: {str(e)}")
                messagebox.showerror("Error", f"Failed to reset stplug-in folder: {str(e)}")
        else:
            self.admin_logger.log_user_activity("reset stplug folder cancelled", {})

    def view_license_history(self):
        """View license key history with modern design"""
        history = self.config_manager.config.get("license_key_history", [])

        if not history:
            messagebox.showinfo("License History", "No license key history found")
            return

        # Create history window
        history_window = ctk.CTkToplevel(self.root)
        history_window.title("License Key History")
        history_window.geometry("800x600")
        history_window.transient(self.root)
        history_window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(history_window, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # Header
        header_label = ctk.CTkLabel(main_frame, text="License Key History",
                                   font=ctk.CTkFont(size=24, weight="bold"))
        header_label.pack(pady=(0, 20))

        # Scrollable frame for history
        scrollable_frame = ctk.CTkScrollableFrame(main_frame, corner_radius=10)
        scrollable_frame.pack(fill="both", expand=True, pady=(0, 20))

        # Display history entries
        for i, entry in enumerate(history):
            # Entry card
            entry_frame = ctk.CTkFrame(scrollable_frame, corner_radius=10)
            entry_frame.pack(fill="x", pady=(0, 15), padx=10)

            entry_content = ctk.CTkFrame(entry_frame, fg_color="transparent")
            entry_content.pack(fill="x", padx=20, pady=20)

            # Header row
            header_row = ctk.CTkFrame(entry_content, fg_color="transparent")
            header_row.pack(fill="x", pady=(0, 10))

            # App name and status
            app_label = ctk.CTkLabel(header_row, text=entry.get("app_name", "Unknown App"),
                                    font=ctk.CTkFont(size=16, weight="bold"))
            app_label.pack(side="left")

            status_color = ("green", "lightgreen") if entry.get("success", False) else ("red", "lightcoral")
            status_text = "✅ Success" if entry.get("success", False) else "❌ Failed"
            status_label = ctk.CTkLabel(header_row, text=status_text,
                                       font=ctk.CTkFont(size=12, weight="bold"),
                                       text_color=status_color)
            status_label.pack(side="right")

            # Details
            details_text = f"License Key: {entry.get('key', 'N/A')}\n"
            details_text += f"App ID: {entry.get('app_id', 'N/A')}\n"
            details_text += f"Timestamp: {entry.get('timestamp', 'N/A')}"

            details_label = ctk.CTkLabel(entry_content, text=details_text,
                                        font=ctk.CTkFont(size=12),
                                        text_color=("gray60", "gray40"),
                                        justify="left")
            details_label.pack(anchor="w")

        # Button frame
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x")

        # Clear history button
        def clear_history():
            if messagebox.askyesno("Confirm", "Clear all license history?"):
                self.config_manager.config["license_key_history"] = []
                self.config_manager.save_config()
                history_window.destroy()
                self.log_message("License history cleared")

        ctk.CTkButton(button_frame, text="Clear History", command=clear_history,
                     fg_color=("red", "darkred"), height=35).pack(side="left")

        # Close button
        ctk.CTkButton(button_frame, text="Close", command=history_window.destroy,
                     height=35).pack(side="right")

    def view_system_logs(self):
        """View comprehensive system logs - admin only"""
        self.admin_logger.log_user_activity("opened system logs viewer", {
            "total_logs": len(self.admin_logger.log_entries),
            "session_id": self.admin_logger.session_id
        })

        # Create logs window
        logs_window = ctk.CTkToplevel(self.root)
        logs_window.title(f"System Logs - Session {self.admin_logger.session_id}")
        logs_window.geometry("1200x800")
        logs_window.transient(self.root)
        logs_window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(logs_window, corner_radius=0, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header with session info
        header_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        header_frame.pack(fill="x", pady=(0, 15))

        header_content = ctk.CTkFrame(header_frame, fg_color="transparent")
        header_content.pack(fill="x", padx=20, pady=15)

        session_info = self.admin_logger.get_session_summary()
        header_text = f"System Logs - Session {session_info['session_id']}\n"
        header_text += f"Duration: {session_info['duration']} | Total Logs: {session_info['total_logs']}"

        header_label = ctk.CTkLabel(header_content, text=header_text,
                                   font=ctk.CTkFont(size=16, weight="bold"))
        header_label.pack()

        # Filters frame
        filters_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        filters_frame.pack(fill="x", pady=(0, 15))

        filters_content = ctk.CTkFrame(filters_frame, fg_color="transparent")
        filters_content.pack(fill="x", padx=20, pady=15)

        # Filter controls
        filter_row1 = ctk.CTkFrame(filters_content, fg_color="transparent")
        filter_row1.pack(fill="x", pady=(0, 10))

        # Level filter
        ctk.CTkLabel(filter_row1, text="Level:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(0, 10))
        level_var = ctk.StringVar(value="All")
        level_menu = ctk.CTkOptionMenu(filter_row1, values=["All", "INFO", "WARNING", "ERROR", "DEBUG"],
                                      variable=level_var, width=100)
        level_menu.pack(side="left", padx=(0, 20))

        # Category filter
        ctk.CTkLabel(filter_row1, text="Category:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(0, 10))
        category_var = ctk.StringVar(value="All")
        categories = ["All"] + list(set(log["category"] for log in self.admin_logger.log_entries))
        category_menu = ctk.CTkOptionMenu(filter_row1, values=categories,
                                         variable=category_var, width=120)
        category_menu.pack(side="left", padx=(0, 20))

        # Search
        ctk.CTkLabel(filter_row1, text="Search:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(0, 10))
        search_var = ctk.StringVar()
        search_entry = ctk.CTkEntry(filter_row1, textvariable=search_var, width=200,
                                   placeholder_text="Search logs...")
        search_entry.pack(side="left", padx=(0, 20))

        # Logs display
        logs_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        logs_frame.pack(fill="both", expand=True, pady=(0, 15))

        logs_text = ctk.CTkTextbox(logs_frame, font=ctk.CTkFont(family="Consolas", size=10))
        logs_text.pack(fill="both", expand=True, padx=20, pady=20)

        def update_logs():
            """Update logs display based on filters"""
            level_filter = level_var.get() if level_var.get() != "All" else None
            category_filter = category_var.get() if category_var.get() != "All" else None
            search_term = search_var.get().strip() if search_var.get().strip() else None

            filtered_logs = self.admin_logger.get_logs(level_filter, category_filter, search_term)

            logs_text.delete("1.0", "end")

            for log in filtered_logs[-500:]:  # Show last 500 logs to prevent UI freeze
                timestamp = log["timestamp"]
                level = log["level"]
                category = log["category"]
                message = log["message"]
                caller = log["caller"]
                details = log.get("details", {})

                log_line = f"[{timestamp}] {level} - {category}\n"
                log_line += f"  Message: {message}\n"
                log_line += f"  Caller: {caller['function']}() at {caller['filename']}:{caller['line']}\n"

                if details:
                    log_line += f"  Details: {json.dumps(details, indent=4)}\n"

                log_line += "-" * 80 + "\n\n"

                logs_text.insert("end", log_line)

            logs_text.see("end")

        # Filter update bindings
        level_var.trace("w", lambda *args: update_logs())
        category_var.trace("w", lambda *args: update_logs())
        search_var.trace("w", lambda *args: update_logs())

        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x")

        # Refresh button
        ctk.CTkButton(buttons_frame, text="Refresh", command=update_logs,
                     height=35, width=100).pack(side="left", padx=(0, 10))

        # Export button
        def export_logs():
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="Export System Logs",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(self.admin_logger.export_logs())
                    messagebox.showinfo("Success", f"Logs exported to {filename}")
                    self.admin_logger.log_user_activity("exported system logs", {
                        "export_filename": filename,
                        "total_logs_exported": len(self.admin_logger.log_entries)
                    })
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to export logs: {str(e)}")
                    self.admin_logger.log_exception("SYSTEM", "Failed to export logs", e)

        ctk.CTkButton(buttons_frame, text="Export Logs", command=export_logs,
                     height=35, width=100).pack(side="left", padx=(0, 10))

        # Clear logs button
        def clear_logs():
            if messagebox.askyesno("Confirm", "Clear all system logs? This cannot be undone."):
                self.admin_logger.clear_logs()
                update_logs()
                messagebox.showinfo("Success", "System logs cleared")

        ctk.CTkButton(buttons_frame, text="Clear Logs", command=clear_logs,
                     height=35, width=100, fg_color=("red", "darkred")).pack(side="left", padx=(0, 10))

        # Close button
        ctk.CTkButton(buttons_frame, text="Close", command=logs_window.destroy,
                     height=35, width=100).pack(side="right")

        # Initial load
        update_logs()

    def change_admin_password(self):
        """Change admin password"""
        self.admin_logger.log_user_activity("password change initiated", {})

        current_password = simpledialog.askstring("Change Password", "Enter current admin password:", show='*')

        if not current_password:
            self.admin_logger.log_user_activity("password change cancelled", {"step": "current_password"})
            return

        if not self.password_manager.verify_password(current_password):
            self.admin_logger.log_user_activity("password change failed", {
                "step": "current_password_verification",
                "reason": "incorrect_current_password"
            })
            messagebox.showerror("Error", "Incorrect current password")
            return

        new_password = simpledialog.askstring("Change Password", "Enter new admin password:", show='*')
        if not new_password:
            self.admin_logger.log_user_activity("password change cancelled", {"step": "new_password"})
            return

        confirm_password = simpledialog.askstring("Change Password", "Confirm new admin password:", show='*')
        if new_password != confirm_password:
            self.admin_logger.log_user_activity("password change failed", {
                "step": "password_confirmation",
                "reason": "passwords_do_not_match"
            })
            messagebox.showerror("Error", "Passwords do not match")
            return

        if len(new_password) < 4:
            self.admin_logger.log_user_activity("password change failed", {
                "step": "password_validation",
                "reason": "password_too_short",
                "password_length": len(new_password)
            })
            messagebox.showerror("Error", "Password must be at least 4 characters long")
            return

        try:
            self.password_manager.change_password(new_password)

            self.admin_logger.log_user_activity("password change successful", {
                "new_password_length": len(new_password),
                "password_strength": "strong" if len(new_password) >= 8 else "medium" if len(new_password) >= 6 else "weak"
            })

            self.log_message("Admin password changed successfully")
            messagebox.showinfo("Success", "Admin password changed successfully")

        except Exception as e:
            self.admin_logger.log_exception("SYSTEM", "Failed to change admin password", e)
            self.admin_logger.log_user_activity("password change failed", {
                "step": "password_save",
                "error_message": str(e)
            })
            self.log_message(f"❌ Error changing password: {str(e)}")
            messagebox.showerror("Error", f"Failed to change password: {str(e)}")

    def run(self):
        """Start the application"""
        self.admin_logger.log("INFO", "SYSTEM", "Application main loop starting", {
            "gui_ready": True,
            "keyauth_initialized": self.keyauth_app is not None,
            "steam_path_configured": bool(self.steam_path)
        })

        try:
            self.root.mainloop()
        except Exception as e:
            self.admin_logger.log_exception("SYSTEM", "Application main loop crashed", e)
            raise
        finally:
            self.admin_logger.log("INFO", "SYSTEM", "Application main loop ended")

def main():
    """Main application entry point"""
    try:
        app = ModernSteamToolsGUI()

        app.admin_logger.log("INFO", "SYSTEM", "Application fully initialized", {
            "initialization_complete": True,
            "total_initialization_time": str(datetime.now() - app.admin_logger.start_time)
        })

        # Save configuration on exit
        def on_closing():
            app.admin_logger.log("INFO", "SYSTEM", "Application shutdown initiated", {
                "session_duration": str(datetime.now() - app.admin_logger.start_time),
                "total_logs_generated": len(app.admin_logger.log_entries),
                "final_steam_path": app.steam_path,
                "final_admin_mode": app.is_admin_mode
            })

            try:
                app.config_manager.save_config()
                app.admin_logger.log("INFO", "SYSTEM", "Configuration saved successfully")
            except Exception as e:
                app.admin_logger.log_exception("SYSTEM", "Failed to save configuration on exit", e)

            app.admin_logger.log("INFO", "SYSTEM", "Application shutdown complete")
            app.root.destroy()

        app.root.protocol("WM_DELETE_WINDOW", on_closing)
        app.run()

    except Exception as e:
        print(f"Fatal application error: {e}")
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
